{% extends 'base.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}Edit Claim {{ claim.claim_number }} - IMT Insurance Claims{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="{% url 'claims:list' %}">Claims</a></li>
                <li class="breadcrumb-item"><a href="{% url 'claims:detail' claim.id %}">{{ claim.claim_number }}</a></li>
                <li class="breadcrumb-item active">Edit</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-edit me-2"></i>Edit Claim {{ claim.claim_number }}
                </h4>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.status.id_for_label }}" class="form-label">Status</label>
                                {{ form.status }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.priority.id_for_label }}" class="form-label">Priority</label>
                                {{ form.priority }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.assigned_adjuster.id_for_label }}" class="form-label">Assigned Adjuster</label>
                        {{ form.assigned_adjuster }}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.approved_amount.id_for_label }}" class="form-label">Approved Amount</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    {{ form.approved_amount }}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.deductible_amount.id_for_label }}" class="form-label">Deductible Amount</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    {{ form.deductible_amount }}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.incident_description.id_for_label }}" class="form-label">Incident Description</label>
                        {{ form.incident_description }}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'claims:detail' claim.id %}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Claim Information
                </h6>
            </div>
            <div class="card-body">
                <p><strong>Claim Number:</strong> {{ claim.claim_number }}</p>
                <p><strong>Customer:</strong> {{ claim.claimant.full_name }}</p>
                <p><strong>Type:</strong> {{ claim.get_claim_type_display }}</p>
                <p><strong>Incident Date:</strong> {{ claim.incident_date|date:"M d, Y" }}</p>
                <p><strong>Submitted:</strong> {{ claim.created_at|date:"M d, Y" }}</p>
                <p class="mb-0"><strong>Last Updated:</strong> {{ claim.updated_at|date:"M d, Y" }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
