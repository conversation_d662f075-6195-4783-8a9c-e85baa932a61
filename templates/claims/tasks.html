{% extends 'base.html' %}
{% load static %}

{% block title %}My Tasks - IMT Insurance Claims{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item active">Tasks</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <select name="status" class="form-select">
                            <option value="">All Statuses</option>
                            {% for value, label in status_choices %}
                                <option value="{{ value }}" {% if current_status == value %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter me-1"></i>Filter
                        </button>
                    </div>
                    <div class="col-md-2">
                        <a href="{% url 'claims:tasks' %}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-times me-1"></i>Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-tasks me-2"></i>My Tasks
                </h4>
            </div>
            <div class="card-body">
                {% if tasks %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Task</th>
                                    <th>Claim</th>
                                    <th>Status</th>
                                    <th>Priority</th>
                                    <th>Due Date</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for task in tasks %}
                                <tr>
                                    <td>
                                        <strong>{{ task.title }}</strong>
                                        {% if task.description %}
                                            <br><small class="text-muted">{{ task.description|truncatechars:50 }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'claims:detail' task.claim.id %}">{{ task.claim.claim_number }}</a>
                                    </td>
                                    <td>
                                        <span class="badge bg-{% if task.status == 'completed' %}success{% elif task.status == 'in_progress' %}warning{% else %}secondary{% endif %}">
                                            {{ task.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge priority-{{ task.priority }}">
                                            {{ task.get_priority_display }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if task.due_date %}
                                            {{ task.due_date|date:"M d, Y" }}
                                            {% if task.due_date < today and task.status != 'completed' %}
                                                <span class="badge bg-danger ms-1">Overdue</span>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">No due date</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ task.created_at|date:"M d, Y" }}</td>
                                    <td>
                                        <a href="{% url 'claims:task_detail' task.id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye me-1"></i>View
                                        </a>
                                        {% if task.status != 'completed' %}
                                            <form method="post" action="{% url 'claims:complete_task' task.id %}" class="d-inline">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-sm btn-outline-success" onclick="return confirm('Mark this task as completed?')">
                                                    <i class="fas fa-check me-1"></i>Complete
                                                </button>
                                            </form>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                        <h5>No Tasks</h5>
                        <p class="text-muted">You don't have any tasks assigned.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
