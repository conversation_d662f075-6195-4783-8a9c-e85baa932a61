{% extends 'base.html' %}
{% load static %}

{% block title %}All Claims - IMT Insurance Claims{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">Dashboard</a></li>
                <li class="breadcrumb-item active">All Claims</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row mb-3">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" name="search" class="form-control" placeholder="Search claims..." value="{{ search_query }}">
                    </div>
                    <div class="col-md-2">
                        <select name="status" class="form-select">
                            <option value="">All Statuses</option>
                            {% for value, label in status_choices %}
                                <option value="{{ value }}" {% if current_status == value %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="priority" class="form-select">
                            <option value="">All Priorities</option>
                            {% for value, label in priority_choices %}
                                <option value="{{ value }}" {% if current_priority == value %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-1"></i>Filter
                        </button>
                    </div>
                    <div class="col-md-2">
                        <a href="{% url 'claims:list' %}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-times me-1"></i>Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h4 class="mb-0">
                    <i class="fas fa-clipboard-list me-2"></i>All Claims
                </h4>
            </div>
            <div class="card-body">
                {% if claims %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Claim Number</th>
                                    <th>Customer</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th>Priority</th>
                                    <th>Adjuster</th>
                                    <th>Submitted</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for claim in claims %}
                                <tr>
                                    <td><strong>{{ claim.claim_number }}</strong></td>
                                    <td>{{ claim.claimant.full_name }}</td>
                                    <td>{{ claim.get_claim_type_display }}</td>
                                    <td>
                                        <span class="badge status-{{ claim.status }}">
                                            {{ claim.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge priority-{{ claim.priority }}">
                                            {{ claim.get_priority_display }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if claim.assigned_adjuster %}
                                            {{ claim.assigned_adjuster.full_name }}
                                        {% else %}
                                            <span class="text-muted">Unassigned</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ claim.created_at|date:"M d, Y" }}</td>
                                    <td>
                                        <a href="{% url 'claims:detail' claim.id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye me-1"></i>View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                        <h5>No Claims Found</h5>
                        <p class="text-muted">No claims match your current filters.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
