/* IMT Insurance Claims System Styling */

:root {
    --imt-blue: #003366;
    --imt-light-blue: #0066cc;
    --imt-green: #006633;
    --imt-gray: #f5f5f5;
    --imt-dark-gray: #333333;
    --imt-border: #ddd;
}

body {
    font-family: 'Arial', sans-serif;
    background-color: #ffffff;
    color: var(--imt-dark-gray);
    line-height: 1.6;
}

/* Header Styling */
.navbar-brand {
    font-weight: bold;
    color: var(--imt-blue) !important;
    font-size: 1.5rem;
}

.navbar {
    background-color: #ffffff !important;
    border-bottom: 2px solid var(--imt-blue);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-nav .nav-link {
    color: var(--imt-blue) !important;
    font-weight: 500;
    padding: 0.75rem 1rem !important;
}

.navbar-nav .nav-link:hover {
    color: var(--imt-light-blue) !important;
    background-color: var(--imt-gray);
    border-radius: 4px;
}

/* Button Styling */
.btn-primary {
    background-color: var(--imt-blue);
    border-color: var(--imt-blue);
    font-weight: 500;
}

.btn-primary:hover {
    background-color: var(--imt-light-blue);
    border-color: var(--imt-light-blue);
}

.btn-success {
    background-color: var(--imt-green);
    border-color: var(--imt-green);
}

.btn-outline-primary {
    color: var(--imt-blue);
    border-color: var(--imt-blue);
}

.btn-outline-primary:hover {
    background-color: var(--imt-blue);
    border-color: var(--imt-blue);
}

/* Card Styling */
.card {
    border: 1px solid var(--imt-border);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: var(--imt-gray);
    border-bottom: 1px solid var(--imt-border);
    font-weight: 600;
    color: var(--imt-blue);
}

/* Dashboard Styling */
.dashboard-card {
    transition: transform 0.2s;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.stat-card {
    text-align: center;
    padding: 2rem 1rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--imt-blue);
}

.stat-label {
    color: var(--imt-dark-gray);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Form Styling */
.form-control:focus {
    border-color: var(--imt-light-blue);
    box-shadow: 0 0 0 0.2rem rgba(0, 102, 204, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--imt-dark-gray);
}

/* Table Styling */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    background-color: var(--imt-blue);
    color: white;
    border: none;
    font-weight: 500;
}

.table tbody tr:hover {
    background-color: var(--imt-gray);
}

/* Status Badges */
.status-submitted { background-color: #17a2b8; }
.status-under-review { background-color: #ffc107; color: #000; }
.status-investigating { background-color: #fd7e14; }
.status-pending-documents { background-color: #dc3545; }
.status-approved { background-color: #28a745; }
.status-denied { background-color: #6c757d; }
.status-closed { background-color: #343a40; }

/* Priority Badges */
.priority-low { background-color: #28a745; }
.priority-medium { background-color: #ffc107; color: #000; }
.priority-high { background-color: #fd7e14; }
.priority-urgent { background-color: #dc3545; }

/* Footer */
.footer {
    background-color: var(--imt-blue);
    color: white;
    padding: 2rem 0;
    margin-top: 3rem;
}

.footer a {
    color: #ccc;
    text-decoration: none;
}

.footer a:hover {
    color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .stat-number {
        font-size: 2rem;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
}

/* Loading spinner */
.spinner-border-imt {
    color: var(--imt-blue);
}

/* Alert styling */
.alert-info {
    background-color: #e7f3ff;
    border-color: var(--imt-light-blue);
    color: var(--imt-blue);
}

/* Sidebar for admin */
.sidebar {
    background-color: var(--imt-gray);
    min-height: calc(100vh - 76px);
    border-right: 1px solid var(--imt-border);
}

.sidebar .nav-link {
    color: var(--imt-dark-gray);
    padding: 0.75rem 1rem;
    border-radius: 4px;
    margin: 0.25rem 0;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: var(--imt-blue);
    color: white;
}

/* File upload styling */
.file-upload-area {
    border: 2px dashed var(--imt-border);
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    background-color: #fafafa;
    transition: border-color 0.3s;
}

.file-upload-area:hover {
    border-color: var(--imt-light-blue);
}

.file-upload-area.dragover {
    border-color: var(--imt-blue);
    background-color: #f0f8ff;
}
